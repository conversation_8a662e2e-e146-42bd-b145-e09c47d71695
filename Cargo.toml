[package]
name = "retoc"
version = "0.1.2"
edition = "2024"
authors = ["trumank", "Archengius"]
repository = "https://github.com/trumank/retoc"
homepage = "https://github.com/trumank/retoc"
license = "MIT"

[profile.release]
debug = true

# The profile that 'dist' will build with
[profile.dist]
inherits = "release"
lto = "thin"

[dependencies]
aes = "0.8.4"
anyhow = "1.0.95"
base64 = "0.22.1"
bitflags = "2.6.0"
blake3 = "1.5.5"
byteorder = "1.5.0"
cityhasher = "0.1.0"
clap = { version = "4.5.26", features = ["derive"] }
flate2 = { version = "1.0.35", features = ["zlib"] }
fs-err = "3.0.0"
hex = "0.4.3"
indexmap = { version = "2.7.0", features = ["serde"] }
indicatif = "0.17.9"
itertools = "0.14.0"
key-mutex = "0.1.3"
lz4_flex = "0.11.3"
oodle_loader = { git = "https://github.com/trumank/repak.git", version = "0.2.2" }
pariter = "0.5.1"
rayon = "1.10.0"
repak = { git = "https://github.com/trumank/repak.git", version = "0.2.2", features = ["oodle"] }
ser-hex = { git = "https://github.com/trumank/ser-hex", version = "0.1.0" }
serde = { version = "1.0.217", features = ["derive"] }
serde_json = "1.0.135"
serde_with = { version = "3.12.0", features = ["hex"] }
sha1 = "0.10.6"
strum = { version = "0.26.3", features = ["derive"] }
tracing = "0.1.41"
typed-path = "0.10.0"
ue_reflection = { git = "https://github.com/trumank/meatloaf.git", version = "0.1.0" }
zstd = "0.13.2"

[dev-dependencies]
pretty_assertions = "1.4.1"
